import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Appointment } from "@/hooks/dashboard/useAppointments";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { Calendar, Clock, Edit, Eye, Stethoscope, User } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface AppointmentTableProps {
  appointments: Appointment[];
  isLoading?: boolean;
  onAppointmentClick?: (appointment: Appointment) => void;
  className?: string;
}

export function AppointmentTable({
  appointments,
  isLoading = false,
  onAppointmentClick,
  className = "",
}: AppointmentTableProps) {
  const { organization } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const navigate = useNavigate();

  const handleAppointmentClick = (appointment: Appointment) => {
    if (onAppointmentClick) {
      onAppointmentClick(appointment);
    } else {
      navigate(`/appointments/${appointment.id}`);
    }
  };

  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.charAt(0) || "";
    const last = lastName?.charAt(0) || "";
    return (first + last).toUpperCase() || "?";
  };

  const formatDateTime = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      const dateFormatted = date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      });

      return dateFormatted;
    } catch {
      return dateStr;
    }
  };

  const formatTime = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      return date.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });
    } catch {
      return "Time TBD";
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      scheduled: { variant: "default" as const, label: "Scheduled" },
      checked_in: { variant: "secondary" as const, label: "Checked In" },
      in_progress: { variant: "default" as const, label: "In Progress" },
      completed: { variant: "secondary" as const, label: "Completed" },
      cancelled: { variant: "destructive" as const, label: "Cancelled" },
      no_show: { variant: "destructive" as const, label: "No Show" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      label: status,
    };

    return (
      <Badge variant={config.variant} className="capitalize">
        {config.label}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className={`border rounded-lg ${className}`}>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Patient</TableHead>
              <TableHead>Date & Time</TableHead>
              <TableHead>Provider</TableHead>
              <TableHead>Department</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Duration</TableHead>
              {isSystemAdmin && organization?.id === "system-admin-all-orgs" && (
                <TableHead>Organization</TableHead>
              )}
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, i) => (
              <TableRow key={i}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 bg-muted animate-pulse rounded-full" />
                    <div className="space-y-1">
                      <div className="h-4 bg-muted animate-pulse rounded w-24" />
                      <div className="h-3 bg-muted animate-pulse rounded w-16" />
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-muted animate-pulse rounded w-32" />
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-muted animate-pulse rounded w-24" />
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-muted animate-pulse rounded w-20" />
                </TableCell>
                <TableCell>
                  <div className="h-6 bg-muted animate-pulse rounded w-16" />
                </TableCell>
                <TableCell>
                  <div className="h-4 bg-muted animate-pulse rounded w-12" />
                </TableCell>
                {isSystemAdmin && organization?.id === "system-admin-all-orgs" && (
                  <TableCell>
                    <div className="h-4 bg-muted animate-pulse rounded w-20" />
                  </TableCell>
                )}
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <div className="h-8 w-8 bg-muted animate-pulse rounded" />
                    <div className="h-8 w-8 bg-muted animate-pulse rounded" />
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (appointments.length === 0) {
    return (
      <div className={`border rounded-lg p-12 text-center ${className}`}>
        <div className="text-muted-foreground">
          <Calendar className="mx-auto h-12 w-12 mb-4" />
          <h3 className="text-lg font-medium mb-2">No appointments found</h3>
          <p>Try adjusting your search criteria or schedule a new appointment.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`border rounded-lg ${className}`}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Patient</TableHead>
            <TableHead>Date & Time</TableHead>
            <TableHead>Provider</TableHead>
            <TableHead>Department</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Duration</TableHead>
            {isSystemAdmin && organization?.id === "system-admin-all-orgs" && (
              <TableHead>Organization</TableHead>
            )}
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {appointments.map((appointment) => (
            <TableRow
              key={appointment.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => handleAppointmentClick(appointment)}
            >
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback className="bg-primary/10 text-primary">
                      {getInitials(
                        appointment.patient?.first_name,
                        appointment.patient?.last_name
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">
                      {appointment.patient_name ||
                        `${appointment.patient?.first_name || ""} ${
                          appointment.patient?.last_name || ""
                        }`.trim() ||
                        "Unknown Patient"}
                    </div>
                    <div className="text-sm text-muted-foreground flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {appointment.reason || "General Consultation"}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  <div className="font-medium">
                    {formatDateTime(appointment.appointment_date)}
                  </div>
                  <div className="text-sm text-muted-foreground flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {formatTime(appointment.appointment_date)}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="space-y-1">
                  <div className="font-medium">
                    {appointment.provider_name ||
                      `${appointment.provider?.first_name || ""} ${
                        appointment.provider?.last_name || ""
                      }`.trim() ||
                      "Unassigned"}
                  </div>
                  <div className="text-sm text-muted-foreground flex items-center gap-1">
                    <Stethoscope className="h-3 w-3" />
                    {appointment.provider?.provider_type || "Provider"}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <div className="font-medium">
                  {appointment.department_name ||
                    appointment.department?.name ||
                    "General"}
                </div>
              </TableCell>
              <TableCell>{getStatusBadge(appointment.status)}</TableCell>
              <TableCell>
                <div className="text-sm">
                  {appointment.duration_minutes || 30} min
                </div>
              </TableCell>
              {isSystemAdmin && organization?.id === "system-admin-all-orgs" && (
                <TableCell>
                  <div className="text-sm font-medium">
                    {appointment.organization_id || "Unknown"}
                  </div>
                </TableCell>
              )}
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/appointments/${appointment.id}`);
                    }}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={(e) => {
                      e.stopPropagation();
                      navigate(`/appointments/${appointment.id}/edit`);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
