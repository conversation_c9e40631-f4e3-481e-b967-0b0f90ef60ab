import { useAuth } from "@/hooks/useAuth";
import { useLocations } from "@/hooks/useLocations";
import { supabase } from "@/lib/supabase";
import { useCallback, useEffect, useRef, useState } from "react";

interface DashboardData {
  patients: any[];
  appointments: any[];
  tasks: any[];
  activityLogs: any[];
  demographics: {
    ageGroups: any[];
    genderDistribution: any[];
  };
  metrics: {
    todayAppointments: number;
    newPatients: number;
    totalProcedures: number;
    revenue: number;
  };
}

export function useDashboardData() {
  const { organization } = useAuth();
  const { selectedLocation } = useLocations();
  const [data, setData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const lastProcessedRef = useRef({
    organizationId: null as string | null,
    locationId: null as string | null,
  });

  const fetchDashboardData = useCallback(async () => {
    if (!organization?.id) return;

    try {
      setIsLoading(true);
      setError(null);

      // Fetch recent patients
      const { data: patients, error: patientsError } = await supabase
        .from("patients")
        .select("*")
        .eq("organization_id", organization.id)
        .order("created_at", { ascending: false })
        .limit(4);

      if (patientsError) throw patientsError;

      // Fetch upcoming appointments with related data
      const { data: appointments, error: appointmentsError } = await supabase
        .from("appointments")
        .select(`
          *,
          patient:patients(id, first_name, last_name, email, phone),
          department:departments(id, name, location_id),
          provider:healthcare_providers(id, first_name, last_name, provider_type)
        `)
        .eq("organization_id", organization.id)
        .gte("appointment_date", new Date().toISOString())
        .order("appointment_date", { ascending: true })
        .limit(4);

      if (appointmentsError) throw appointmentsError;

      // Fetch pending tasks
      const { data: tasks, error: tasksError } = await supabase
        .from("tasks")
        .select("*")
        .eq("organization_id", organization.id)
        .in("status", ["pending", "in_progress"])
        .order("priority", { ascending: false })
        .order("due_date", { ascending: true, nullsFirst: false })
        .limit(4);

      if (tasksError) throw tasksError;

      // Update last processed ref
      lastProcessedRef.current = {
        organizationId: organization.id,
        locationId: selectedLocation?.id || null,
      };

      // Set all data at once
      setData({
        patients: patients || [],
        appointments: appointments || [],
        tasks: tasks || [],
        activityLogs: [],
        demographics: {
          ageGroups: [],
          genderDistribution: [],
        },
        metrics: {
          todayAppointments: 0,
          newPatients: 0,
          totalProcedures: 0,
          revenue: 0,
        },
      });
    } catch (err) {
      console.error("Error fetching dashboard data:", err);
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [organization?.id, selectedLocation?.id]);

  useEffect(() => {
    const organizationId = organization?.id;
    const locationId = selectedLocation?.id;

    // Check if we need to process (values have changed)
    const lastProcessed = lastProcessedRef.current;
    const hasChanged =
      lastProcessed.organizationId !== organizationId ||
      lastProcessed.locationId !== locationId;

    if (!hasChanged) {
      return; // No changes, skip processing
    }

    fetchDashboardData();
  }, [organization?.id, selectedLocation?.id, fetchDashboardData]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchDashboardData,
  };
} 