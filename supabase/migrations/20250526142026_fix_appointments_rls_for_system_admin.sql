-- Fix appointments RLS policy to include system_admin role
-- This allows system admins to view all appointments across all organizations

-- Drop the existing policy
DROP POLICY IF EXISTS "appointments_access" ON "public"."appointments";

-- Create updated policy that includes system_admin
CREATE POLICY "appointments_access" ON "public"."appointments" FOR SELECT USING (
CASE
    WHEN (( SELECT "auth"."role"() AS "role") = 'patient'::"text") THEN ("patient_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = 'provider'::"text") THEN ("provider_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'system_admin'::"text"])) THEN true
    ELSE false
END);

-- Also update the update policy to include system_admin
DROP POLICY IF EXISTS "optimized_appointments_update" ON "public"."appointments";

CREATE POLICY "optimized_appointments_update" ON "public"."appointments" FOR UPDATE USING (
CASE
    WHEN (( SELECT "auth"."role"() AS "role") = 'patient'::"text") THEN ("patient_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = 'provider'::"text") THEN ("provider_id" = ( SELECT "auth"."uid"() AS "uid"))
    WHEN (( SELECT "auth"."role"() AS "role") = ANY (ARRAY['admin'::"text", 'clinical_staff'::"text", 'system_admin'::"text"])) THEN true
    ELSE false
END);